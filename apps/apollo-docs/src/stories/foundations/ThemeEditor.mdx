import { Meta } from "@storybook/addon-docs/blocks"
import { ThemeEditor } from "./theme-editor"

<Meta title="Foundations/Theme Editor" tags={["docs"]} />

# Theme Editor

The Apollo Theme Editor is an interactive tool that allows you to customize your Apollo design system theme in real-time. Similar to the Ant Design theme editor, it provides a comprehensive interface for modifying colors, typography, spacing, border radius, and shadow properties while seeing the changes applied to live component previews.

## Features

- **Real-time Preview**: See your theme changes applied to live components instantly
- **Comprehensive Controls**: Customize colors, typography, spacing, border radius, and shadows
- **Export/Import**: Save and share theme configurations as JSON files
- **Responsive Design**: Works on desktop and mobile devices
- **Apollo Integration**: Built specifically for the Apollo design system

## Interactive Theme Editor

Use the theme editor below to customize your Apollo theme. Changes are applied in real-time to the preview components on the right.

<ThemeEditor />

## Usage

### Basic Usage

```tsx
import { ThemeEditor } from "@apollo/ui"

function MyThemeEditor() {
  const handleThemeChange = (theme) => {
    console.log("Theme updated:", theme)
    // Apply theme to your application
  }

  return (
    <ThemeEditor onThemeChange={handleThemeChange} />
  )
}
```

### With Initial Theme

```tsx
import { ThemeEditor } from "@apollo/ui"

const initialTheme = {
  colors: {
    primary: {
      40: "#1e40af",
      50: "#3b82f6",
      60: "#60a5fa",
      // ... other shades
    },
    // ... other color groups
  },
  // ... other theme properties
}

function MyThemeEditor() {
  return (
    <ThemeEditor
      initialTheme={initialTheme}
      onThemeChange={(theme) => {
        // Handle theme changes
      }}
    />
  )
}
```

## Theme Structure

The theme editor works with a structured theme configuration that includes:

### Colors

- **Primary Colors**: Main brand colors used for primary actions and highlights
- **Secondary Colors**: Supporting colors for secondary actions and elements
- **Neutral Colors**: Grayscale colors for text, borders, and backgrounds

Each color group includes multiple shades (40, 50, 60, 70, 80, 90, 95) following Material Design 3 color system principles.

### Typography

- **Font Families**: Primary and secondary font stacks
- **Font Sizes**: Scale from XS to 3XL for different text elements
- **Font Weights**: Normal, medium, semibold, and bold weights

### Spacing

Consistent spacing scale from XS to 4XL for margins, padding, and gaps.

### Border Radius

Border radius scale from none to full (circular) for different component styles.

### Shadows

Shadow definitions for elevation and depth effects.

## Export and Import

### Exporting Themes

1. Click the "Export Theme" button in the theme editor
2. Copy the JSON configuration or download it as a file
3. Use the exported configuration in your application

### Importing Themes

1. Click the "Import Theme" button
2. Upload a JSON file or paste the configuration
3. The theme editor will validate and apply the imported theme

### Example Theme Configuration

```json
{
  "colors": {
    "primary": {
      "40": "#016E2E",
      "50": "#2C8745",
      "60": "#49A25C",
      "70": "#64BE74",
      "80": "#7FDA8E",
      "90": "#9BF7A7",
      "95": "#C5FFC8"
    },
    "secondary": {
      "40": "#5F5E5F",
      "50": "#787777",
      "60": "#929091",
      "70": "#ADABAB",
      "80": "#C8C6C6",
      "90": "#E5E2E2",
      "95": "#F3F0F0"
    },
    "neutral": {
      "10": "#1C1B1C",
      "20": "#313031",
      "30": "#474647",
      "40": "#5F5E5F",
      "50": "#787777",
      "60": "#929091",
      "70": "#ADABAB",
      "80": "#C8C6C6",
      "90": "#E5E2E2",
      "95": "#F3F0F0",
      "99": "#F8F7F7"
    }
  },
  "typography": {
    "fontFamily": {
      "primary": "IBM Plex Sans Thai",
      "secondary": "IBM Plex Sans Thai"
    },
    "fontSize": {
      "xs": "12px",
      "sm": "14px",
      "md": "16px",
      "lg": "18px",
      "xl": "20px",
      "2xl": "24px",
      "3xl": "28px"
    },
    "fontWeight": {
      "normal": "400",
      "medium": "500",
      "semibold": "600",
      "bold": "700"
    }
  },
  "spacing": {
    "xs": "4px",
    "sm": "8px",
    "md": "16px",
    "lg": "24px",
    "xl": "32px",
    "2xl": "48px",
    "3xl": "64px",
    "4xl": "96px"
  },
  "borderRadius": {
    "none": "0px",
    "sm": "4px",
    "md": "6px",
    "lg": "8px",
    "xl": "12px",
    "2xl": "16px",
    "full": "9999px"
  },
  "shadows": {
    "sm": "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    "md": "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    "lg": "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
    "xl": "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
    "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)"
  }
}
```

## Integration with Apollo Theme System

The theme editor generates theme configurations that are compatible with Apollo's theme system. The exported themes can be used directly with the `createThemeV2` function:

```tsx
import { createThemeV2, Theme } from "@apollo/ui"

// Import your exported theme configuration
import customTheme from "./my-custom-theme.json"

const apolloTheme = createThemeV2({
  tokens: {
    color: {
      "custom-primary": customTheme.colors.primary,
      "custom-secondary": customTheme.colors.secondary,
      "custom-neutral": customTheme.colors.neutral,
    },
    base: {
      color: {
        primary: {
          40: "{color.custom-primary.40}",
          50: "{color.custom-primary.50}",
          60: "{color.custom-primary.60}",
          // ... other mappings
        },
      },
    },
  },
})

function App() {
  return (
    <Theme theme={apolloTheme}>
      {/* Your app components */}
    </Theme>
  )
}
```

## Best Practices

1. **Start with Default Values**: Begin with the default Apollo theme and make incremental changes
2. **Test Accessibility**: Ensure sufficient color contrast for accessibility compliance
3. **Maintain Consistency**: Keep consistent spacing and sizing relationships
4. **Export Regularly**: Save your theme configurations as you work
5. **Test Across Components**: Use the preview panel to see how changes affect different components
6. **Document Changes**: Keep notes about your theme customizations for team collaboration

## Accessibility Considerations

- **Color Contrast**: Ensure text colors meet WCAG contrast requirements against their backgrounds
- **Focus States**: Verify that focus indicators are visible with your color choices
- **Color Independence**: Don't rely solely on color to convey information
- **Typography**: Maintain readable font sizes and appropriate line heights

## Browser Support

The theme editor supports all modern browsers and includes fallbacks for older browsers. Color picker functionality requires browsers that support the HTML5 color input type.