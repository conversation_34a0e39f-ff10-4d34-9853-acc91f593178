.themeExportImport {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.actionGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Export Modal */
.exportModal {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.exportActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.exportTextarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
}

/* Import Modal */
.importModal {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.importMethods {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.fileImport {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.fileInput {
  display: none;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--apl-alias-color-light-outline-outline-variant);
}

.divider span {
  background: var(--apl-alias-color-light-surface-surface);
  padding: 0 12px;
  position: relative;
  z-index: 1;
}

.textImport {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.importTextarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
}

.importActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid var(--apl-alias-color-light-outline-outline-variant);
}

/* Responsive design */
@media (max-width: 768px) {
  .exportActions {
    flex-direction: column;
  }
  
  .importActions {
    flex-direction: column-reverse;
  }
  
  .buttons {
    gap: 6px;
  }
}
