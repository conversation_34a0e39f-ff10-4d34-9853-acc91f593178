.themeEditor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 800px;
  background: var(--apl-alias-color-light-surface-surface);
  border: 1px solid var(--apl-alias-color-light-outline-outline);
  border-radius: var(--apl-alias-radius-radius4);
  overflow: hidden;
}

.header {
  padding: 24px;
  border-bottom: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  background: var(--apl-alias-color-light-surface-surface-container-lowest);
}

.content {
  display: flex;
  flex: 1;
  min-height: 0;
}

.controls {
  width: 400px;
  border-right: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  display: flex;
  flex-direction: column;
  background: var(--apl-alias-color-light-surface-surface-container-low);
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  background: var(--apl-alias-color-light-surface-surface-container-lowest);
  overflow-x: auto;
}

.tab {
  flex: 1;
  min-width: 80px;
  padding: 12px 8px;
  border: none;
  background: transparent;
  color: var(--apl-alias-color-light-on-surface-on-surface-variant);
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab:hover {
  background: var(--apl-alias-color-light-surface-surface-container);
  color: var(--apl-alias-color-light-on-surface-on-surface);
}

.tabActive {
  color: var(--apl-alias-color-light-primary-primary);
  border-bottom-color: var(--apl-alias-color-light-primary-primary);
  background: var(--apl-alias-color-light-surface-surface-container);
}

.controlsContent {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.actions {
  border-top: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  padding: 16px;
  background: var(--apl-alias-color-light-surface-surface-container-lowest);
}

.preview {
  flex: 1;
  overflow-y: auto;
  background: var(--apl-alias-color-light-surface-surface);
}

/* Responsive design */
@media (max-width: 1200px) {
  .content {
    flex-direction: column;
  }
  
  .controls {
    width: 100%;
    max-height: 400px;
    border-right: none;
    border-bottom: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  }
  
  .tabs {
    flex-wrap: wrap;
  }
  
  .tab {
    flex: none;
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .themeEditor {
    height: auto;
    max-height: none;
  }
  
  .header {
    padding: 16px;
  }
  
  .controlsContent {
    padding: 12px;
  }
  
  .actions {
    padding: 12px;
  }
}
