import React, { useState, use<PERSON><PERSON>back, useMemo } from "react"
import { Theme, createThemeV2, Typography } from "@apollo/ui"
import type { ThemeConfig } from "@apollo/ui"

import { ThemeControls } from "./ThemeControls"
import { ThemePreview } from "./ThemePreview"
import { ThemeExportImport } from "./ThemeExportImport"
import styles from "./ThemeEditor.module.css"

export interface ThemeEditorState {
  colors: {
    primary: {
      40: string
      50: string
      60: string
      70: string
      80: string
      90: string
      95: string
    }
    secondary: {
      40: string
      50: string
      60: string
      70: string
      80: string
      90: string
      95: string
    }
    neutral: {
      10: string
      20: string
      30: string
      40: string
      50: string
      60: string
      70: string
      80: string
      90: string
      95: string
      99: string
    }
  }
  typography: {
    fontFamily: {
      primary: string
      secondary: string
    }
    fontSize: {
      xs: string
      sm: string
      md: string
      lg: string
      xl: string
      "2xl": string
      "3xl": string
    }
    fontWeight: {
      normal: string
      medium: string
      semibold: string
      bold: string
    }
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
    "2xl": string
    "3xl": string
    "4xl": string
  }
  borderRadius: {
    none: string
    sm: string
    md: string
    lg: string
    xl: string
    "2xl": string
    full: string
  }
  shadows: {
    sm: string
    md: string
    lg: string
    xl: string
    "2xl": string
  }
}

const defaultThemeState: ThemeEditorState = {
  colors: {
    primary: {
      40: "#016E2E",
      50: "#2C8745",
      60: "#49A25C",
      70: "#64BE74",
      80: "#7FDA8E",
      90: "#9BF7A7",
      95: "#C5FFC8",
    },
    secondary: {
      40: "#5F5E5F",
      50: "#787777",
      60: "#929091",
      70: "#ADABAB",
      80: "#C8C6C6",
      90: "#E5E2E2",
      95: "#F3F0F0",
    },
    neutral: {
      10: "#1C1B1C",
      20: "#313031",
      30: "#474647",
      40: "#5F5E5F",
      50: "#787777",
      60: "#929091",
      70: "#ADABAB",
      80: "#C8C6C6",
      90: "#E5E2E2",
      95: "#F3F0F0",
      99: "#F8F7F7",
    },
  },
  typography: {
    fontFamily: {
      primary: "IBM Plex Sans Thai",
      secondary: "IBM Plex Sans Thai",
    },
    fontSize: {
      xs: "12px",
      sm: "14px",
      md: "16px",
      lg: "18px",
      xl: "20px",
      "2xl": "24px",
      "3xl": "28px",
    },
    fontWeight: {
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
    },
  },
  spacing: {
    xs: "4px",
    sm: "8px",
    md: "16px",
    lg: "24px",
    xl: "32px",
    "2xl": "48px",
    "3xl": "64px",
    "4xl": "96px",
  },
  borderRadius: {
    none: "0px",
    sm: "4px",
    md: "6px",
    lg: "8px",
    xl: "12px",
    "2xl": "16px",
    full: "9999px",
  },
  shadows: {
    sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
    xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
    "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)",
  },
}

export interface ThemeEditorProps {
  initialTheme?: Partial<ThemeEditorState>
  onThemeChange?: (theme: ThemeConfig) => void
}

export function ThemeEditor({ initialTheme, onThemeChange }: ThemeEditorProps) {
  const [themeState, setThemeState] = useState<ThemeEditorState>({
    ...defaultThemeState,
    ...initialTheme,
  })
  const [activeTab, setActiveTab] = useState<
    "colors" | "typography" | "spacing" | "borderRadius" | "shadows"
  >("colors")

  const apolloTheme = useMemo(() => {
    const theme = createThemeV2({
      tokens: {
        color: {
          "custom-primary": themeState.colors.primary,
          "custom-secondary": themeState.colors.secondary,
          "custom-neutral": themeState.colors.neutral,
        },
        base: {
          color: {
            primary: {
              40: "{color.custom-primary.40}",
              50: "{color.custom-primary.50}",
              60: "{color.custom-primary.60}",
              70: "{color.custom-primary.70}",
              80: "{color.custom-primary.80}",
              90: "{color.custom-primary.90}",
              95: "{color.custom-primary.95}",
            },
            secondary: {
              40: "{color.custom-secondary.40}",
              50: "{color.custom-secondary.50}",
              60: "{color.custom-secondary.60}",
              70: "{color.custom-secondary.70}",
              80: "{color.custom-secondary.80}",
              90: "{color.custom-secondary.90}",
              95: "{color.custom-secondary.95}",
            },
            neutral: {
              10: "{color.custom-neutral.10}",
              20: "{color.custom-neutral.20}",
              30: "{color.custom-neutral.30}",
              40: "{color.custom-neutral.40}",
              50: "{color.custom-neutral.50}",
              60: "{color.custom-neutral.60}",
              70: "{color.custom-neutral.70}",
              80: "{color.custom-neutral.80}",
              90: "{color.custom-neutral.90}",
              95: "{color.custom-neutral.95}",
              99: "{color.custom-neutral.99}",
            },
          },
        },
      },
    })

    onThemeChange?.(theme)
    return theme
  }, [themeState, onThemeChange])

  const handleThemeStateChange = useCallback(
    (updates: Partial<ThemeEditorState>) => {
      setThemeState((prev) => ({ ...prev, ...updates }))
    },
    []
  )

  const handleImportTheme = useCallback((importedTheme: Partial<ThemeEditorState>) => {
    setThemeState((prev) => ({ ...prev, ...importedTheme }))
  }, [])

  const handleResetTheme = useCallback(() => {
    setThemeState(defaultThemeState)
  }, [])

  return (
    <div className={styles.themeEditor}>
      <div className={styles.header}>
        <Typography level="titleLarge">Apollo Theme Editor</Typography>
        <Typography level="bodyMedium" color="secondary">
          Customize your Apollo design system theme in real-time
        </Typography>
      </div>

      <div className={styles.content}>
        <div className={styles.controls}>
          <div className={styles.tabs}>
            {(["colors", "typography", "spacing", "borderRadius", "shadows"] as const).map(
              (tab) => (
                <button
                  key={tab}
                  className={`${styles.tab} ${activeTab === tab ? styles.tabActive : ""}`}
                  onClick={() => setActiveTab(tab)}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              )
            )}
          </div>

          <div className={styles.controlsContent}>
            <ThemeControls
              activeTab={activeTab}
              themeState={themeState}
              onThemeStateChange={handleThemeStateChange}
            />
          </div>

          <div className={styles.actions}>
            <ThemeExportImport
              themeState={themeState}
              onImport={handleImportTheme}
              onReset={handleResetTheme}
            />
          </div>
        </div>

        <div className={styles.preview}>
          <Theme theme={apolloTheme}>
            <ThemePreview />
          </Theme>
        </div>
      </div>
    </div>
  )
}
