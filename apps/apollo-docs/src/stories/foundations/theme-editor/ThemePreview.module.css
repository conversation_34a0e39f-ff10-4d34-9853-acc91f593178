.themePreview {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.previewHeader {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--apl-alias-color-light-outline-outline-variant);
}

.previewGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  align-items: start;
}

.previewCard {
  background: var(--apl-alias-color-light-surface-surface-container-low);
  border: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  border-radius: var(--apl-alias-radius-radius3);
  overflow: hidden;
}

.cardTitle {
  padding: 16px 20px 12px;
  background: var(--apl-alias-color-light-surface-surface-container-lowest);
  border-bottom: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  margin: 0;
}

.cardContent {
  padding: 20px;
}

/* Button Group */
.buttonGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
}

/* Typography Group */
.typographyGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Form Group */
.formGroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Selection Group */
.selectionGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radioGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

/* Feedback Group */
.feedbackGroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.badgeGroup {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.chipGroup {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Navigation Group */
.navigationGroup {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Layout Group */
.layoutGroup {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cardExample {
  margin-top: 16px;
}

.sampleCard {
  background: var(--apl-alias-color-light-surface-surface);
  border: 1px solid var(--apl-alias-color-light-outline-outline-variant);
  border-radius: var(--apl-alias-radius-radius3);
  padding: 16px;
  box-shadow: var(--apl-alias-elevation-level1);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.cardActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
}

/* Interactive Group */
.interactiveGroup {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.iconButtons {
  display: flex;
  gap: 8px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .previewGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .themePreview {
    padding: 16px;
  }
  
  .previewGrid {
    gap: 16px;
  }
  
  .cardContent {
    padding: 16px;
  }
  
  .buttonGroup {
    align-items: stretch;
  }
  
  .badgeGroup {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .iconButtons {
    justify-content: center;
  }
}
