import React, { useState, useRef } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Alert } from "@apollo/ui"
import { Download, Upload, RotateLeft, Copy, Check } from "@design-systems/apollo-icons"
import type { ThemeEditorState } from "./ThemeEditor"
import styles from "./ThemeExportImport.module.css"

export interface ThemeExportImportProps {
  themeState: ThemeEditorState
  onImport: (theme: Partial<ThemeEditorState>) => void
  onReset: () => void
}

export function ThemeExportImport({ themeState, onImport, onReset }: ThemeExportImportProps) {
  const [exportModalOpen, setExportModalOpen] = useState(false)
  const [importModalOpen, setImportModalOpen] = useState(false)
  const [importValue, setImportValue] = useState("")
  const [importError, setImportError] = useState("")
  const [copied, setCopied] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const exportTheme = () => {
    const themeJson = JSON.stringify(themeState, null, 2)
    return themeJson
  }

  const downloadTheme = () => {
    const themeJson = exportTheme()
    const blob = new Blob([themeJson], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `apollo-theme-${new Date().toISOString().split("T")[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const copyTheme = async () => {
    try {
      const themeJson = exportTheme()
      await navigator.clipboard.writeText(themeJson)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy theme:", error)
    }
  }

  const validateAndImportTheme = (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString)
      
      // Basic validation of theme structure
      const requiredKeys = ["colors", "typography", "spacing", "borderRadius", "shadows"]
      const missingKeys = requiredKeys.filter(key => !(key in parsed))
      
      if (missingKeys.length > 0) {
        throw new Error(`Missing required theme properties: ${missingKeys.join(", ")}`)
      }

      // Validate colors structure
      if (parsed.colors) {
        const colorGroups = ["primary", "secondary", "neutral"]
        for (const group of colorGroups) {
          if (parsed.colors[group] && typeof parsed.colors[group] !== "object") {
            throw new Error(`Invalid color group structure: ${group}`)
          }
        }
      }

      // Validate typography structure
      if (parsed.typography) {
        const typographyKeys = ["fontFamily", "fontSize", "fontWeight"]
        for (const key of typographyKeys) {
          if (parsed.typography[key] && typeof parsed.typography[key] !== "object") {
            throw new Error(`Invalid typography structure: ${key}`)
          }
        }
      }

      onImport(parsed)
      setImportModalOpen(false)
      setImportValue("")
      setImportError("")
    } catch (error) {
      setImportError(error instanceof Error ? error.message : "Invalid JSON format")
    }
  }

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      validateAndImportTheme(content)
    }
    reader.readAsText(file)
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleTextImport = () => {
    if (!importValue.trim()) {
      setImportError("Please enter a theme configuration")
      return
    }
    validateAndImportTheme(importValue)
  }

  const handleResetConfirm = () => {
    if (window.confirm("Are you sure you want to reset the theme to default values? This action cannot be undone.")) {
      onReset()
    }
  }

  return (
    <div className={styles.themeExportImport}>
      <div className={styles.actionGroup}>
        <Typography level="titleSmall">Theme Actions</Typography>
        
        <div className={styles.buttons}>
          <Button
            variant="outline"
            size="small"
            startDecorator={<Download size={16} />}
            onClick={() => setExportModalOpen(true)}
          >
            Export Theme
          </Button>
          
          <Button
            variant="outline"
            size="small"
            startDecorator={<Upload size={16} />}
            onClick={() => setImportModalOpen(true)}
          >
            Import Theme
          </Button>
          
          <Button
            variant="text"
            size="small"
            color="negative"
            startDecorator={<RotateLeft size={16} />}
            onClick={handleResetConfirm}
          >
            Reset to Default
          </Button>
        </div>
      </div>

      {/* Export Modal */}
      <Modal.Root
        open={exportModalOpen}
        onOpenChange={(open) => setExportModalOpen(open)}
        className="max-w-[600px]"
      >
        <Modal.Header>
          <Typography level="titleMedium">Export Theme Configuration</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <div className={styles.exportModal}>
            <Typography level="bodyMedium" style={{ marginBottom: "16px" }}>
              Copy the theme configuration below or download it as a JSON file.
            </Typography>

            <div className={styles.exportActions}>
              <Button
                variant="outline"
                size="small"
                startDecorator={copied ? <Check size={16} /> : <Copy size={16} />}
                onClick={copyTheme}
              >
                {copied ? "Copied!" : "Copy to Clipboard"}
              </Button>

              <Button
                variant="filled"
                size="small"
                startDecorator={<Download size={16} />}
                onClick={downloadTheme}
              >
                Download JSON
              </Button>
            </div>

            <Textarea
              value={exportTheme()}
              readOnly
              rows={20}
              className={styles.exportTextarea}
            />
          </div>
        </Modal.Content>
      </Modal.Root>

      {/* Import Modal */}
      <Modal.Root
        open={importModalOpen}
        onOpenChange={(open) => {
          if (!open) {
            setImportModalOpen(false)
            setImportValue("")
            setImportError("")
          }
        }}
        className="max-w-[600px]"
      >
        <Modal.Header>
          <Typography level="titleMedium">Import Theme Configuration</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <div className={styles.importModal}>
            <Typography level="bodyMedium" style={{ marginBottom: "16px" }}>
              Import a theme configuration by uploading a JSON file or pasting the configuration below.
            </Typography>

            {importError && (
              <Alert color="error" style={{ marginBottom: "16px" }}>
                <Typography level="bodySmall">Import Error: {importError}</Typography>
              </Alert>
            )}

            <div className={styles.importMethods}>
              <div className={styles.fileImport}>
                <Typography level="labelMedium" style={{ marginBottom: "8px" }}>
                  Upload JSON File
                </Typography>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json"
                  onChange={handleFileImport}
                  className={styles.fileInput}
                />
                <Button
                  variant="outline"
                  size="small"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Choose File
                </Button>
              </div>

              <div className={styles.divider}>
                <Typography level="labelSmall" color="secondary">
                  OR
                </Typography>
              </div>

              <div className={styles.textImport}>
                <Typography level="labelMedium" style={{ marginBottom: "8px" }}>
                  Paste JSON Configuration
                </Typography>
                <Textarea
                  value={importValue}
                  onChange={(e) => {
                    setImportValue((e.target as HTMLTextAreaElement).value)
                    setImportError("")
                  }}
                  placeholder="Paste your theme configuration JSON here..."
                  rows={15}
                  className={styles.importTextarea}
                />
              </div>
            </div>
          </div>
        </Modal.Content>
        <Modal.Footer>
          <div className={styles.importActions}>
            <Button
              variant="text"
              onClick={() => {
                setImportModalOpen(false)
                setImportValue("")
                setImportError("")
              }}
            >
              Cancel
            </Button>
            <Button
              variant="filled"
              onClick={handleTextImport}
              disabled={!importValue.trim()}
            >
              Import Theme
            </Button>
          </div>
        </Modal.Footer>
      </Modal.Root>

      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleFileImport}
        style={{ display: "none" }}
      />
    </div>
  )
}
