.themeControls {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Color Controls */
.colorControls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.colorGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.colorGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.colorPicker {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.colorInputGroup {
  display: flex;
  gap: 8px;
  align-items: center;
}

.colorInput {
  width: 40px;
  height: 32px;
  border: 1px solid var(--apl-alias-color-light-outline-outline);
  border-radius: var(--apl-alias-radius-radius2);
  cursor: pointer;
  background: none;
}

.colorInput::-webkit-color-swatch-wrapper {
  padding: 0;
}

.colorInput::-webkit-color-swatch {
  border: none;
  border-radius: var(--apl-alias-radius-radius1);
}

.colorTextInput {
  flex: 1;
  font-family: monospace;
  font-size: 12px;
}

/* Typography Controls */
.typographyControls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.controlGroup {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.select {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.selectInput {
  padding: 8px 12px;
  border: 1px solid var(--apl-alias-color-light-outline-outline);
  border-radius: var(--apl-alias-radius-radius2);
  background: var(--apl-alias-color-light-surface-surface);
  color: var(--apl-alias-color-light-on-surface-on-surface);
  font-size: 14px;
}

.selectInput:focus {
  outline: none;
  border-color: var(--apl-alias-color-light-primary-primary);
  box-shadow: 0 0 0 1px var(--apl-alias-color-light-primary-primary);
}

/* Slider Controls */
.slider {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sliderHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sliderInput {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--apl-alias-color-light-surface-surface-container-high);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.sliderInput::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--apl-alias-color-light-primary-primary);
  cursor: pointer;
  border: 2px solid var(--apl-alias-color-light-surface-surface);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.sliderInput::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--apl-alias-color-light-primary-primary);
  cursor: pointer;
  border: 2px solid var(--apl-alias-color-light-surface-surface);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Spacing Controls */
.spacingControls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Border Radius Controls */
.borderRadiusControls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Shadow Controls */
.shadowControls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.shadowControl {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.shadowInput {
  font-family: monospace;
  font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .colorGrid {
    grid-template-columns: 1fr;
  }
  
  .colorInputGroup {
    flex-direction: column;
    align-items: stretch;
  }
  
  .colorInput {
    width: 100%;
    height: 40px;
  }
}
