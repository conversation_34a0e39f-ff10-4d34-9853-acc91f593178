import React from "react"
import { Typo<PERSON>, Input, Button } from "@apollo/ui"
import type { ThemeEditorState } from "./ThemeEditor"
import styles from "./ThemeControls.module.css"

interface ColorPickerProps {
  label: string
  value: string
  onChange: (value: string) => void
}

function ColorPicker({ label, value, onChange }: ColorPickerProps) {
  return (
    <div className={styles.colorPicker}>
      <Typography level="labelMedium">{label}</Typography>
      <div className={styles.colorInputGroup}>
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={styles.colorInput}
        />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          size="small"
          className={styles.colorTextInput}
        />
      </div>
    </div>
  )
}

interface SliderProps {
  label: string
  value: string
  onChange: (value: string) => void
  min: number
  max: number
  step: number
  unit: string
}

function Slider({ label, value, onChange, min, max, step, unit }: SliderProps) {
  const numericValue = parseFloat(value.replace(unit, ""))

  return (
    <div className={styles.slider}>
      <div className={styles.sliderHeader}>
        <Typography level="labelMedium">{label}</Typography>
        <Typography level="labelSmall" color="secondary">
          {value}
        </Typography>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={numericValue}
        onChange={(e) => onChange(`${e.target.value}${unit}`)}
        className={styles.sliderInput}
      />
    </div>
  )
}

interface SelectProps {
  label: string
  value: string
  onChange: (value: string) => void
  options: { value: string; label: string }[]
}

function Select({ label, value, onChange, options }: SelectProps) {
  return (
    <div className={styles.select}>
      <Typography level="labelMedium">{label}</Typography>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={styles.selectInput}
      >
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}

interface ColorControlsProps {
  colors: ThemeEditorState["colors"]
  onChange: (colors: ThemeEditorState["colors"]) => void
}

function ColorControls({ colors, onChange }: ColorControlsProps) {
  const updateColorGroup = (
    group: keyof ThemeEditorState["colors"],
    shade: string,
    value: string
  ) => {
    onChange({
      ...colors,
      [group]: {
        ...colors[group],
        [shade]: value,
      },
    })
  }

  return (
    <div className={styles.colorControls}>
      <div className={styles.colorGroup}>
        <Typography level="titleSmall">Primary Colors</Typography>
        <div className={styles.colorGrid}>
          {Object.entries(colors.primary).map(([shade, color]) => (
            <ColorPicker
              key={shade}
              label={`Primary ${shade}`}
              value={color}
              onChange={(value) => updateColorGroup("primary", shade, value)}
            />
          ))}
        </div>
      </div>

      <div className={styles.colorGroup}>
        <Typography level="titleSmall">Secondary Colors</Typography>
        <div className={styles.colorGrid}>
          {Object.entries(colors.secondary).map(([shade, color]) => (
            <ColorPicker
              key={shade}
              label={`Secondary ${shade}`}
              value={color}
              onChange={(value) => updateColorGroup("secondary", shade, value)}
            />
          ))}
        </div>
      </div>

      <div className={styles.colorGroup}>
        <Typography level="titleSmall">Neutral Colors</Typography>
        <div className={styles.colorGrid}>
          {Object.entries(colors.neutral).map(([shade, color]) => (
            <ColorPicker
              key={shade}
              label={`Neutral ${shade}`}
              value={color}
              onChange={(value) => updateColorGroup("neutral", shade, value)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

interface TypographyControlsProps {
  typography: ThemeEditorState["typography"]
  onChange: (typography: ThemeEditorState["typography"]) => void
}

function TypographyControls({ typography, onChange }: TypographyControlsProps) {
  const fontFamilyOptions = [
    { value: "IBM Plex Sans Thai", label: "IBM Plex Sans Thai" },
    { value: "Inter", label: "Inter" },
    { value: "Roboto", label: "Roboto" },
    { value: "Open Sans", label: "Open Sans" },
    { value: "Lato", label: "Lato" },
    { value: "Montserrat", label: "Montserrat" },
    { value: "Poppins", label: "Poppins" },
  ]

  const fontWeightOptions = [
    { value: "300", label: "Light (300)" },
    { value: "400", label: "Normal (400)" },
    { value: "500", label: "Medium (500)" },
    { value: "600", label: "Semibold (600)" },
    { value: "700", label: "Bold (700)" },
    { value: "800", label: "Extra Bold (800)" },
  ]

  return (
    <div className={styles.typographyControls}>
      <div className={styles.controlGroup}>
        <Typography level="titleSmall">Font Families</Typography>
        <Select
          label="Primary Font"
          value={typography.fontFamily.primary}
          onChange={(value) =>
            onChange({
              ...typography,
              fontFamily: { ...typography.fontFamily, primary: value },
            })
          }
          options={fontFamilyOptions}
        />
        <Select
          label="Secondary Font"
          value={typography.fontFamily.secondary}
          onChange={(value) =>
            onChange({
              ...typography,
              fontFamily: { ...typography.fontFamily, secondary: value },
            })
          }
          options={fontFamilyOptions}
        />
      </div>

      <div className={styles.controlGroup}>
        <Typography level="titleSmall">Font Sizes</Typography>
        {Object.entries(typography.fontSize).map(([size, value]) => (
          <Slider
            key={size}
            label={`${size.toUpperCase()} Size`}
            value={value}
            onChange={(newValue) =>
              onChange({
                ...typography,
                fontSize: { ...typography.fontSize, [size]: newValue },
              })
            }
            min={8}
            max={48}
            step={1}
            unit="px"
          />
        ))}
      </div>

      <div className={styles.controlGroup}>
        <Typography level="titleSmall">Font Weights</Typography>
        {Object.entries(typography.fontWeight).map(([weight, value]) => (
          <Select
            key={weight}
            label={`${weight.charAt(0).toUpperCase() + weight.slice(1)} Weight`}
            value={value}
            onChange={(newValue) =>
              onChange({
                ...typography,
                fontWeight: { ...typography.fontWeight, [weight]: newValue },
              })
            }
            options={fontWeightOptions}
          />
        ))}
      </div>
    </div>
  )
}

interface SpacingControlsProps {
  spacing: ThemeEditorState["spacing"]
  onChange: (spacing: ThemeEditorState["spacing"]) => void
}

function SpacingControls({ spacing, onChange }: SpacingControlsProps) {
  return (
    <div className={styles.spacingControls}>
      <Typography level="titleSmall">Spacing Scale</Typography>
      {Object.entries(spacing).map(([size, value]) => (
        <Slider
          key={size}
          label={`${size.toUpperCase()} Spacing`}
          value={value}
          onChange={(newValue) =>
            onChange({ ...spacing, [size]: newValue })
          }
          min={0}
          max={128}
          step={4}
          unit="px"
        />
      ))}
    </div>
  )
}

interface BorderRadiusControlsProps {
  borderRadius: ThemeEditorState["borderRadius"]
  onChange: (borderRadius: ThemeEditorState["borderRadius"]) => void
}

function BorderRadiusControls({ borderRadius, onChange }: BorderRadiusControlsProps) {
  return (
    <div className={styles.borderRadiusControls}>
      <Typography level="titleSmall">Border Radius Scale</Typography>
      {Object.entries(borderRadius).map(([size, value]) => (
        <Slider
          key={size}
          label={`${size.charAt(0).toUpperCase() + size.slice(1)} Radius`}
          value={value === "9999px" ? "999px" : value}
          onChange={(newValue) =>
            onChange({
              ...borderRadius,
              [size]: size === "full" && newValue === "999px" ? "9999px" : newValue,
            })
          }
          min={0}
          max={size === "full" ? 999 : 32}
          step={1}
          unit="px"
        />
      ))}
    </div>
  )
}

interface ShadowControlsProps {
  shadows: ThemeEditorState["shadows"]
  onChange: (shadows: ThemeEditorState["shadows"]) => void
}

function ShadowControls({ shadows, onChange }: ShadowControlsProps) {
  return (
    <div className={styles.shadowControls}>
      <Typography level="titleSmall">Shadow Scale</Typography>
      <Typography level="bodySmall" color="secondary">
        Shadows are complex CSS values. Use the preview to see changes.
      </Typography>
      {Object.entries(shadows).map(([size, value]) => (
        <div key={size} className={styles.shadowControl}>
          <Typography level="labelMedium">
            {size.toUpperCase()} Shadow
          </Typography>
          <Input
            value={value}
            onChange={(e) =>
              onChange({ ...shadows, [size]: e.target.value })
            }
            size="small"
            className={styles.shadowInput}
          />
        </div>
      ))}
    </div>
  )
}

export interface ThemeControlsProps {
  activeTab: "colors" | "typography" | "spacing" | "borderRadius" | "shadows"
  themeState: ThemeEditorState
  onThemeStateChange: (updates: Partial<ThemeEditorState>) => void
}

export function ThemeControls({
  activeTab,
  themeState,
  onThemeStateChange,
}: ThemeControlsProps) {
  const renderControls = () => {
    switch (activeTab) {
      case "colors":
        return (
          <ColorControls
            colors={themeState.colors}
            onChange={(colors) => onThemeStateChange({ colors })}
          />
        )
      case "typography":
        return (
          <TypographyControls
            typography={themeState.typography}
            onChange={(typography) => onThemeStateChange({ typography })}
          />
        )
      case "spacing":
        return (
          <SpacingControls
            spacing={themeState.spacing}
            onChange={(spacing) => onThemeStateChange({ spacing })}
          />
        )
      case "borderRadius":
        return (
          <BorderRadiusControls
            borderRadius={themeState.borderRadius}
            onChange={(borderRadius) => onThemeStateChange({ borderRadius })}
          />
        )
      case "shadows":
        return (
          <ShadowControls
            shadows={themeState.shadows}
            onChange={(shadows) => onThemeStateChange({ shadows })}
          />
        )
      default:
        return null
    }
  }

  return <div className={styles.themeControls}>{renderControls()}</div>
}
