import React, { useState } from "react"
import {
  Button,
  Input,
  Typography,
  Checkbox,
  Switch,
  Radio,
  Textarea,
  Alert,
  Badge,
  <PERSON>,
} from "@apollo/ui"
import { CheckCircle, Setting, Heart, Star } from "@design-systems/apollo-icons"
import styles from "./ThemePreview.module.css"

interface PreviewCardProps {
  title: string
  children: React.ReactNode
}

function PreviewCard({ title, children }: PreviewCardProps) {
  return (
    <div className={styles.previewCard}>
      <Typography level="titleSmall" className={styles.cardTitle}>
        {title}
      </Typography>
      <div className={styles.cardContent}>{children}</div>
    </div>
  )
}

export function ThemePreview() {
  const [inputValue, setInputValue] = useState("")
  const [textareaValue, setTextareaValue] = useState("")
  const [checkboxChecked, setCheckboxChecked] = useState(false)
  const [switchChecked, setSwitchChecked] = useState(false)
  const [radioValue, setRadioValue] = useState("option1")

  return (
    <div className={styles.themePreview}>
      <div className={styles.previewHeader}>
        <Typography level="titleLarge">Live Preview</Typography>
        <Typography level="bodyMedium" color="secondary">
          See your theme changes in real-time
        </Typography>
      </div>

      <div className={styles.previewGrid}>
        <PreviewCard title="Buttons">
          <div className={styles.buttonGroup}>
            <Button variant="filled">Primary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="text">Text Button</Button>
            <Button variant="filled" color="negative">
              Negative Button
            </Button>
            <Button variant="filled" size="small">
              Small Button
            </Button>
            <Button variant="filled" disabled>
              Disabled Button
            </Button>
          </div>
        </PreviewCard>

        <PreviewCard title="Typography">
          <div className={styles.typographyGroup}>
            <Typography level="displayLarge">Display Large</Typography>
            <Typography level="displayMedium">Display Medium</Typography>
            <Typography level="headlineLarge">Headline Large</Typography>
            <Typography level="headlineMedium">Headline Medium</Typography>
            <Typography level="titleLarge">Title Large</Typography>
            <Typography level="titleMedium">Title Medium</Typography>
            <Typography level="bodyLarge">Body Large Text</Typography>
            <Typography level="bodyMedium">Body Medium Text</Typography>
            <Typography level="bodySmall">Body Small Text</Typography>
            <Typography level="labelLarge">Label Large</Typography>
            <Typography level="labelMedium">Label Medium</Typography>
            <Typography level="labelSmall">Label Small</Typography>
          </div>
        </PreviewCard>

        <PreviewCard title="Form Controls">
          <div className={styles.formGroup}>
            <Input
              label="Text Input"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Enter text here..."
            />
            <Input
              label="Input with Error"
              error="This field is required"
              placeholder="Error state"
            />
            <Textarea
              label="Textarea"
              value={textareaValue}
              onChange={(e) => setTextareaValue(e.target.value)}
              placeholder="Enter longer text here..."
              rows={3}
            />
          </div>
        </PreviewCard>

        <PreviewCard title="Selection Controls">
          <div className={styles.selectionGroup}>
            <Checkbox
              checked={checkboxChecked}
              onChange={setCheckboxChecked}
              label="Checkbox Option"
            />
            <Checkbox checked disabled label="Disabled Checkbox" />
            
            <Switch
              checked={switchChecked}
              onChange={setSwitchChecked}
              label="Switch Toggle"
            />
            <Switch checked disabled label="Disabled Switch" />

            <div className={styles.radioGroup}>
              <Radio
                name="radio-group"
                value="option1"
                checked={radioValue === "option1"}
                onChange={() => setRadioValue("option1")}
                label="Radio Option 1"
              />
              <Radio
                name="radio-group"
                value="option2"
                checked={radioValue === "option2"}
                onChange={() => setRadioValue("option2")}
                label="Radio Option 2"
              />
              <Radio
                name="radio-group"
                value="option3"
                checked={radioValue === "option3"}
                onChange={() => setRadioValue("option3")}
                label="Radio Option 3"
              />
            </div>
          </div>
        </PreviewCard>

        <PreviewCard title="Feedback & Status">
          <div className={styles.feedbackGroup}>
            <Alert severity="info" title="Information">
              This is an informational alert message.
            </Alert>
            <Alert severity="success" title="Success">
              Operation completed successfully!
            </Alert>
            <Alert severity="warning" title="Warning">
              Please review your input.
            </Alert>
            <Alert severity="error" title="Error">
              Something went wrong.
            </Alert>

            <div className={styles.badgeGroup}>
              <Badge label="5">
                <Button variant="outline">Notifications</Button>
              </Badge>
              <Badge label="99">
                <Button variant="outline">Messages</Button>
              </Badge>
            </div>

            <div className={styles.chipGroup}>
              <Chip label="Default Chip" />
              <Chip label="Clickable Chip" onClick={() => {}} />
              <Chip label="Icon Chip" startDecorator={<Star />} />
            </div>
          </div>
        </PreviewCard>

        <PreviewCard title="Layout Components">
          <div className={styles.layoutGroup}>
            <div className={styles.cardExample}>
              <div className={styles.sampleCard}>
                <div className={styles.cardHeader}>
                  <Typography level="titleMedium">Sample Card</Typography>
                  <Setting size={20} />
                </div>
                <Typography level="bodyMedium">
                  This is a sample card component showing how your theme affects
                  layout components with borders, shadows, and spacing.
                </Typography>
                <div className={styles.cardActions}>
                  <Button variant="text" size="small">
                    Cancel
                  </Button>
                  <Button variant="filled" size="small">
                    Confirm
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </PreviewCard>

        <PreviewCard title="Interactive Elements">
          <div className={styles.interactiveGroup}>
            <div className={styles.iconButtons}>
              <Button variant="text" size="small">
                <Heart size={16} />
              </Button>
              <Button variant="outline" size="small">
                <Star size={16} />
              </Button>
              <Button variant="filled" size="small">
                <CheckCircle size={16} />
              </Button>
            </div>
          </div>
        </PreviewCard>
      </div>
    </div>
  )
}
